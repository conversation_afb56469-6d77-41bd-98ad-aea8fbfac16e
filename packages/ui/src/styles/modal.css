/*
 * SPDX-License-Identifier: AGPL-3.0-only
 * modal.css
 * Copyright (C) 2025 Nextify Limited
 *
 * Modal component styles using Tailwind CSS v4 syntax
 * Provides specialized styling for project details and other modal components
 */

/* Modal container utilities */
@utility modal-container {
  @apply fixed inset-0 z-50 flex items-center justify-center p-4;
}

@utility modal-overlay {
  @apply fixed inset-0 bg-black/50 backdrop-blur-sm;
}

/* Project details modal specific styles */
@utility project-modal-content {
  @apply bg-background border border-border shadow-2xl;
  @apply flex flex-col;
  @apply w-full max-w-7xl h-[90vh] max-h-[900px];
  @apply rounded-xl;
  
  /* Mobile adjustments */
  @media (max-width: 640px) {
    @apply w-[98vw] h-[95vh] rounded-lg;
  }
  
  /* Tablet adjustments */
  @media (min-width: 641px) and (max-width: 1024px) {
    @apply w-[95vw];
  }
  
  /* Desktop adjustments */
  @media (min-width: 1025px) {
    @apply w-[90vw];
  }
  
  /* Large desktop adjustments */
  @media (min-width: 1280px) {
    @apply w-[85vw];
  }
}

/* Modal header styles */
@utility modal-header {
  @apply flex items-center gap-3 px-6 py-4 border-b bg-muted/5;
  @apply flex-shrink-0;
  
  /* Mobile adjustments */
  @media (max-width: 640px) {
    @apply px-4 py-3;
  }
}

@utility modal-header-gradient {
  @apply bg-gradient-to-r from-primary/5 to-background;
}

@utility modal-title {
  @apply text-xl font-semibold tracking-tight text-foreground;
  @apply truncate;
  
  /* Mobile adjustments */
  @media (max-width: 640px) {
    @apply text-lg;
  }
}

@utility modal-description {
  @apply text-sm text-muted-foreground mt-1;
  
  /* Hide on mobile */
  @media (max-width: 640px) {
    @apply hidden;
  }
}

/* Modal body layout */
@utility modal-body {
  @apply flex-1 flex min-h-0;
  
  /* Mobile: stack vertically */
  @media (max-width: 768px) {
    @apply flex-col;
  }
  
  /* Desktop: side by side */
  @media (min-width: 769px) {
    @apply flex-row;
  }
}

/* Sidebar styles */
@utility modal-sidebar {
  @apply bg-muted/5 border-r flex-shrink-0;
  @apply w-full;
  
  /* Mobile: full width with bottom border */
  @media (max-width: 768px) {
    @apply border-r-0 border-b;
  }
  
  /* Tablet */
  @media (min-width: 769px) and (max-width: 1024px) {
    @apply w-56 min-w-56;
  }
  
  /* Desktop */
  @media (min-width: 1025px) {
    @apply w-64 min-w-64;
  }
}

@utility modal-sidebar-content {
  @apply py-4 px-2;
  
  /* Mobile: horizontal scroll */
  @media (max-width: 768px) {
    @apply overflow-x-auto;
  }
  
  /* Desktop: vertical scroll */
  @media (min-width: 769px) {
    @apply overflow-y-auto max-h-[600px];
  }
}

/* Content area styles */
@utility modal-content-area {
  @apply flex-1 flex flex-col min-h-0;
}

@utility modal-scroll-area {
  @apply flex-1 px-6 py-6;
  
  /* Mobile adjustments */
  @media (max-width: 640px) {
    @apply px-4 py-4;
  }
  
  /* Tablet adjustments */
  @media (min-width: 641px) and (max-width: 1024px) {
    @apply px-6 py-5;
  }
  
  /* Desktop adjustments */
  @media (min-width: 1025px) {
    @apply px-8 py-6;
  }
}

/* Navigation styles */
@utility modal-nav-group {
  @apply mb-6;
}

@utility modal-nav-group-title {
  @apply text-sm font-medium text-muted-foreground mb-2;
}

@utility modal-nav-item {
  @apply flex items-center justify-between w-full px-3 py-2 text-sm rounded-md;
  @apply transition-colors duration-200;
  @apply text-muted-foreground hover:bg-muted hover:text-foreground;
}

@utility modal-nav-item-active {
  @apply bg-primary text-primary-foreground;
  @apply hover:bg-primary/90;
}

@utility modal-nav-item-disabled {
  @apply opacity-70 cursor-not-allowed;
}

/* Form section styles */
@utility modal-form-section {
  @apply space-y-4 w-full max-w-none;
}

@utility modal-section-header {
  @apply space-y-2;
}

@utility modal-section-title {
  @apply text-lg font-semibold tracking-tight text-foreground;
}

@utility modal-section-divider {
  @apply h-px bg-border/60;
}

/* Loading and error states */
@utility modal-loading-container {
  @apply flex items-center justify-center h-full py-10;
}

@utility modal-error-container {
  @apply flex items-center justify-center h-full py-10;
}

@utility modal-error-content {
  @apply text-center p-4 space-y-4;
}

@utility modal-error-icon {
  @apply h-10 w-10 text-amber-500 mx-auto;
}

@utility modal-error-title {
  @apply text-lg font-medium text-red-500;
}

@utility modal-error-description {
  @apply text-sm text-muted-foreground;
}

/* Animation utilities */
@utility modal-fade-in {
  @apply animate-in fade-in-0 duration-200;
}

@utility modal-fade-out {
  @apply animate-out fade-out-0 duration-200;
}

@utility modal-scale-in {
  @apply animate-in zoom-in-95 duration-200;
}

@utility modal-scale-out {
  @apply animate-out zoom-out-95 duration-200;
}

@utility modal-slide-in-from-bottom {
  @apply animate-in slide-in-from-bottom-4 duration-300;
}

@utility modal-slide-out-to-bottom {
  @apply animate-out slide-out-to-bottom-4 duration-300;
}

/* Accessibility utilities */
@utility modal-focus-visible {
  @apply focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2;
}

@utility modal-sr-only {
  @apply sr-only;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .modal-nav-item-active {
    @apply border-2 border-primary-foreground;
  }
  
  .modal-error-title {
    @apply border-l-4 border-red-500 pl-3;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .modal-fade-in,
  .modal-fade-out,
  .modal-scale-in,
  .modal-scale-out,
  .modal-slide-in-from-bottom,
  .modal-slide-out-to-bottom {
    @apply duration-0;
  }
}
